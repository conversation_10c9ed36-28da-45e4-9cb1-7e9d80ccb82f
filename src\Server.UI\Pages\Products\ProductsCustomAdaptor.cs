using CleanArchitecture.Blazor.Application.Features.Products.Queries.Pagination;
using MediatR;
using Syncfusion.Blazor.Data;
using Syncfusion.Blazor;

namespace CleanArchitecture.Blazor.Server.UI.Pages.Products;

// Custom Data Adaptor for Syncfusion Grid
public class ProductsCustomAdaptor : DataAdaptor
{
    [Inject] public IMediator Mediator { get; set; } = default!;
    [Inject] public IServiceProvider ServiceProvider { get; set; } = default!;

    public override async Task<object> ReadAsync(DataManagerRequest dm, string key = null)
    {
        using var scope = ServiceProvider.CreateScope();
        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
        
        var query = new ProductsWithPaginationQuery
        {
            PageNumber = (dm.Skip / dm.Take) + 1,
            PageSize = dm.Take,
            Keyword = dm.Search?.Count > 0 ? dm.Search[0].Key : string.Empty,
            OrderBy = dm.Sorted?.FirstOrDefault()?.Name ?? "Id",
            SortDirection = dm.Sorted?.FirstOrDefault()?.Direction == "descending" 
                ? "Descending" 
                : "Ascending"
        };

        var result = await mediator.Send(query);
        
        return dm.RequiresCounts 
            ? new DataResult { Result = result.Items, Count = result.TotalItems }
            : result.Items;
    }
}
